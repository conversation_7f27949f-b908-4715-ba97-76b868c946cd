import { useDispatch } from 'react-redux'

import { useConfirmDeliveryPlanMutation } from 'src/modules/delivery/api/plans/useConfirmPlanMutation'
import { useDeleteDeliveryPlanMutation } from 'src/modules/delivery/api/plans/useDeletePlanMutation'
import type { FetchDeliveryPlanDetails } from 'src/modules/delivery/api/plans/useDeliveryPlanListsQuery'
//slice
import {
  clickedDuplicateDeliveryPlan,
  clickedResetCreateDeliveryPlan,
} from 'src/modules/delivery/slice'
import Dialog from 'src/modules/delivery/utils/dialog/dialog-configurator'
//component
import Snackbar from 'src/modules/delivery/utils/snackbar-configuraor'
import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  plan: FetchDeliveryPlanDetails.Return
}

export const usePlanContextItems = ({ plan }: Props) => {
  const dispatch = useDispatch()
  const confirmPlan = useConfirmDeliveryPlanMutation()
  const deletePlan = useDeleteDeliveryPlanMutation()
  const handleAssignNow = () => {
    const totalStops = plan && plan.orderedStopIds ? plan.orderedStopIds.length : 0
    if (plan.targetDriverId && totalStops > 0) {
      confirmPlan.mutate(
        {
          planId: Number(plan.planId),
        },
        {
          onSuccess: () => {
            Snackbar.success(
              ctIntl.formatMessage(
                {
                  id: 'delivery.rightPanel.planAssign.successful',
                },
                {
                  values: {
                    count: 1,
                  },
                },
              ),
            )
            dispatch(clickedResetCreateDeliveryPlan())
          },
        },
      )
    }
  }
  const handleDelete = () =>
    deletePlan.mutate(
      {
        planId: Number(plan.planId),
      },
      {
        onSuccess: () => {
          Snackbar.success(
            ctIntl.formatMessage(
              {
                id: 'delivery.rightPanel.planDelete.successful',
              },
              {
                values: {
                  count: 1,
                },
              },
            ),
          )
          dispatch(clickedResetCreateDeliveryPlan())
        },
      },
    )

  const handleOnDelete = () => {
    Dialog.alert({
      title: ctIntl.formatMessage(
        {
          id: 'delivery.rightPanel.planPromptDelete.successful',
        },
        {
          values: {
            count: '',
          },
        },
      ),
      content: ctIntl.formatMessage({
        id: 'delivery.rightPanel.multiJobDelete.successful.subtitle',
      }),
      onResult: () => handleDelete(),
      confirmButtonLabel: ctIntl.formatMessage({
        id: 'Delete',
      }),
    })
  }

  const contextItems = !plan.assigning
    ? [
        {
          value: 'assign-now',
          label: ctIntl.formatMessage({
            id: 'delivery.rightPanel.planInfo.option.assignNow',
          }),
          onClick: () => {
            handleAssignNow()
          },
          disabled:
            !plan.targetDriverId ||
            (plan.orderedStopIds && plan.orderedStopIds.length <= 0),
        },
        {
          value: 'duplicate',
          label: ctIntl.formatMessage({
            id: 'global.duplicate',
          }),
          onClick: () => {
            dispatch(clickedDuplicateDeliveryPlan(plan.planId))
          },
        },
        {
          value: 'delete',
          label: ctIntl.formatMessage({
            id: 'delivery.rightPanel.driverInfo.option.delete',
          }),
          onClick: () => {
            handleOnDelete()
          },
        },
      ]
    : []

  return {
    contextItems: [...contextItems],
  }
}
