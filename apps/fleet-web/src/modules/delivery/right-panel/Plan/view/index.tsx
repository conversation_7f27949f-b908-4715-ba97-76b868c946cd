/* eslint-disable react-hooks/react-compiler */
/* eslint-disable no-nested-ternary */
import {
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { isEmpty, isNil } from 'lodash'
import { Tooltip as MuiTooltip } from '@karoo-ui/core'
import { useQueryClient } from '@tanstack/react-query'
import moment from 'moment'
import {
  DragDropContext,
  Droppable,
  type OnDragEndResponder,
} from 'react-beautiful-dnd'
import { useDispatch, useSelector } from 'react-redux'
import styled from 'styled-components'

import Icon from 'src/components/Icon'
import { useDebouncedValue } from 'src/hooks'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
//context
import { DeliveryContext } from 'src/modules/delivery'
//api
import useDeliveryGetDriversQuery, {
  type FetchGetDeliveryDrivers,
} from 'src/modules/delivery/api/drivers/useDeliveryGetDrivers'
import useDeliveryJobListByFilters, {
  type FetchDeliveryJobListsByDriverId,
} from 'src/modules/delivery/api/drivers/useDeliveryJobListByFilters'
import useImportJobTemplateList from 'src/modules/delivery/api/import-job-template/useImportJobTemplateList'
import { StopStatus, type JobStopReturn } from 'src/modules/delivery/api/jobs/types'
//types
import type { PlanRecurrence } from 'src/modules/delivery/api/plans/types'
import { useConfirmDeliveryPlanMutation } from 'src/modules/delivery/api/plans/useConfirmPlanMutation'
import { useDeleteDeliveryPlanMutation } from 'src/modules/delivery/api/plans/useDeletePlanMutation'
import useDeliveryPlanOptimizeStopsMutation from 'src/modules/delivery/api/plans/useDeliveryPlanOptimizeStopsMutation'
import {
  useEditPlanMutation,
  type EditDeliveryPlan,
} from 'src/modules/delivery/api/plans/useEditPlanMutation'
import Box from 'src/modules/delivery/components/Box'
import Button from 'src/modules/delivery/components/Buttons'
import MetricCheckCard from 'src/modules/delivery/components/Card/MetricCheckCard'
import Checkbox from 'src/modules/delivery/components/Checkbox'
import ContextMenu from 'src/modules/delivery/components/ContextMenu'
import DateTimePicker from 'src/modules/delivery/components/Datepicker/datetimepicker'
import DriverDropdown from 'src/modules/delivery/components/DriverDropdown'
import InputField from 'src/modules/delivery/components/InputTextField'
import JobStepItem from 'src/modules/delivery/components/Job/Timeline/Step'
import JobItem from 'src/modules/delivery/components/List/Jobs/Item/ItemView'
import LoadingIndicator from 'src/modules/delivery/components/Loading'
import LoadingOverlay from 'src/modules/delivery/components/Loading/PanelOverlay'
import Panel from 'src/modules/delivery/components/Panel'
import Tabs from 'src/modules/delivery/components/Tab'
import { themeType } from 'src/modules/delivery/components/Theme'
import Tooltip from 'src/modules/delivery/components/Tooltip'
//hooks
import useDeliveryPlanCapacityData from 'src/modules/delivery/hooks/plan/useDeliveryPlanCapacityData'
//contexture
import { usePlanStopsContextItems } from 'src/modules/delivery/hooks/right-click-context/usePlanStopsContextItems'
import useGetActiveRoutingSettings from 'src/modules/delivery/hooks/settings/useGetActiveRoutingSettings'
import DeliveryImport from 'src/modules/delivery/import/job'
import { formatShiftTime } from 'src/modules/delivery/right-panel/Driver/form'
import JobRouteCard from 'src/modules/delivery/right-panel/Driver/view/JobRouteCard'
import RouteOptimizeButton from 'src/modules/delivery/right-panel/Driver/view/optimize-route'
import {
  changedJobStopsSequence,
  getIsChangingJobStopsSequence,
  hoveredDriverJobStop,
  startedChangingJobStopsSequence,
  stoppedChangingJobStopsSequence,
  stoppedHoveringDriverJobStop,
  unmountedDriverRightPanel,
} from 'src/modules/delivery/right-panel/Driver/view/slice'
//styled
import {
  RouteDriverStatusSVG,
  RouterDriverText,
  RouteStatusWrapper,
  ThinScrollBarContainer,
} from 'src/modules/delivery/right-panel/Driver/view/styled'
import { resetjobStopsBeingCreated } from 'src/modules/delivery/right-panel/Job/View/slice'
import usePlanStops from 'src/modules/delivery/right-panel/Plan/view/usePlanStops'
import { ROUTING_PROVIDER_API } from 'src/modules/delivery/setting/routing'
import {
  clickedDuplicateDeliveryPlan,
  clickedOverviewJobDetail,
  clickedPlanPanelCreateJobDirectly,
  clickedResetCreateDeliveryPlan,
  selectedActiveImport,
} from 'src/modules/delivery/slice'
import {
  HeaderCustomIconButton,
  HeaderIconButton,
  HelperText,
} from 'src/modules/delivery/styled/GlobalStyles'
//components
import {
  CONTEXTURE_MENU_VALUE,
  DELIVERY_DATE_TIME_PAYLOAD_FORMAT,
  DRIVER_STATUS_TO_ID,
  ETA_SIMULATOR_STATUS_ID,
  IMPORT_TYPE,
  JOB_STOP_TYPE_ID,
} from 'src/modules/delivery/utils/constants'
import Dialog from 'src/modules/delivery/utils/dialog/dialog-configurator'
//helpers
import { formatMetricCardData } from 'src/modules/delivery/utils/helpers'
import Snackbar from 'src/modules/delivery/utils/snackbar-configuraor'
import { ctIntl } from 'src/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

//svg
import ImportSVG from 'assets/svg/Delivery/menu-import.svg'
import DriverNoJobTodo from 'assets/svg/Delivery/panel-no-job.svg'
import RecurrenceIcon from 'assets/svg/Delivery/recurrence-icon.svg'

import { handleShowRoutingError } from '../../AssignJob/AssignJob'
import ETASimulator from '../../component/eta-simulate-button'
import RepeatedFieldDropdown from '../form/RepeatedFilterDropdown'
//constants
import {
  DAYS,
  FREQUENCIES,
  FREQUENCY,
  type Option,
} from '../form/RepeatedFilterDropdown/contants'
import useDeliveryPlanStopsData from './useDeliveryPlanStopsData'

export const PlanViewPanel = () => {
  const queryClient = useQueryClient()
  const dispatch = useDispatch()
  const { deliverySettings } = useContext(DeliveryContext)

  const [isFoldedPlanDetails, setIsFoldedPlanDetails] = useState(false)
  const [isFoldedPlanJobStopDetails, setIsFoldedPlanJobStopDetails] = useState(false)
  const [currentTab, setCurrentTab] = useState<string>('1')
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null)
  const [scheduledTime, setScheduledTime] = useState('')
  const [isDateTimeError, setIsDateTimeError] = useState<string | null>(null)
  const [showModal, setShowModal] = useState(false)
  const startedChangingJobStopsSequenceHandler = (index: number) => {
    setDraggingIndex(index)
    dispatch(startedChangingJobStopsSequence())
  }

  const { data: templates } = useImportJobTemplateList()

  const isChangingJobStopsSequence = useSelector(getIsChangingJobStopsSequence)

  const optimizePlanStops = useDeliveryPlanOptimizeStopsMutation()
  const deliveryDriverListQuery = useDeliveryGetDriversQuery()

  const { parsedPlan, focusedDeliveryPlanId } = usePlanStops(
    setScheduledTime,
    setIsDateTimeError,
  )

  const onDriverJobStopMouseOver = (stopId: string) => {
    if (!isDragging) {
      dispatch(hoveredDriverJobStop(stopId))
    }
  }

  const onDriverJobStopMouseLeave = () => {
    if (!isDragging) {
      dispatch(stoppedHoveringDriverJobStop())
    }
  }

  const driver: FetchGetDeliveryDrivers.Return[number] | undefined = useMemo(() => {
    if (
      deliveryDriverListQuery.status === 'success' &&
      parsedPlan &&
      parsedPlan.targetDriverId
    ) {
      return deliveryDriverListQuery.data.find(
        (item) => item.driverId === parsedPlan.targetDriverId,
      )
    }
    return undefined
  }, [parsedPlan, deliveryDriverListQuery])

  const defaultETAISOString = useMemo(() => {
    let date = moment().startOf('day').toISOString()
    if (driver && driver.shiftTimeStart) {
      date = formatShiftTime(driver.shiftTimeStart)?.toISOString() || date
    }

    return date
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [driver, parsedPlan])

  const [etaSimulateValue, setETASimulateValue] = useState<Date | null>(null)

  const deliveryPlanStopInfo = useDeliveryPlanStopsData(
    focusedDeliveryPlanId,
    parsedPlan?.orderedStopIds || [],
    {
      defaultETAISOString,
      etaSimulateValue,
    },
  )

  const [planNameInputValue, setPlanNameInputValue] = useState<string>(parsedPlan.name)
  const planNameDebounce = useDebouncedValue<string>(planNameInputValue.trim(), 5000)
  const [isPlanNameToSave, setIsPlanNameToSave] = useState(true)

  const [recurrenceData, setRecurrenceData] = useState<PlanRecurrence>(
    parsedPlan.recurrence,
  )
  const [isRecurrence, setIsRecurrence] = useState(Boolean(parsedPlan.recurrence))
  const [toggleInput, setToggleInput] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = useState(false)

  const defaultStops = useMemo(
    () => ({
      stops: deliveryPlanStopInfo.stops,
      currentStops: deliveryPlanStopInfo.currentStops,
      futureStops: deliveryPlanStopInfo.futureStops,
      parsedJobs: deliveryPlanStopInfo.parsedJobs,
      deduplicatedJobs: deliveryPlanStopInfo.deduplicatedJobs,
      isPlanStopsLoading: deliveryPlanStopInfo.isPlanStopsLoading,
    }),
    [deliveryPlanStopInfo],
  )

  const [jobStopInfo, setJobStopInfo] = useState(defaultStops)
  const totalStops = [...jobStopInfo.currentStops, ...jobStopInfo.futureStops].length

  const confirmPlan = useConfirmDeliveryPlanMutation()
  const deletePlan = useDeleteDeliveryPlanMutation()
  const handleAssignNow = useCallback(() => {
    if (parsedPlan.targetDriverId && totalStops > 0) {
      confirmPlan.mutate(
        {
          planId: Number(parsedPlan.planId),
        },
        {
          onSuccess: () => {
            Snackbar.success(
              ctIntl.formatMessage(
                {
                  id: 'delivery.rightPanel.planAssign.successful',
                },
                {
                  values: {
                    count: 1,
                  },
                },
              ),
            )
            dispatch(clickedResetCreateDeliveryPlan())
          },
        },
      )
    }
  }, [confirmPlan, dispatch, parsedPlan, totalStops])
  const handleDelete = useCallback(
    () =>
      deletePlan.mutate(
        {
          planId: Number(parsedPlan.planId),
        },
        {
          onSuccess: () => {
            Snackbar.success(
              ctIntl.formatMessage(
                {
                  id: 'delivery.rightPanel.planDelete.successful',
                },
                {
                  values: {
                    count: 1,
                  },
                },
              ),
            )
            dispatch(clickedResetCreateDeliveryPlan())
          },
        },
      ),
    [deletePlan, dispatch, parsedPlan.planId],
  )

  const handleOnDelete = useCallback(() => {
    Dialog.alert({
      title: ctIntl.formatMessage(
        {
          id: 'delivery.rightPanel.planPromptDelete.successful',
        },
        {
          values: {
            count: '',
          },
        },
      ),
      content: ctIntl.formatMessage({
        id: 'delivery.rightPanel.multiJobDelete.successful.subtitle',
      }),
      onResult: () => handleDelete(),
      confirmButtonLabel: ctIntl.formatMessage({
        id: 'Delete',
      }),
    })
  }, [handleDelete])

  //Remove memoization here to avoid incorrect update on each plan selection
  const dataWithPlanId: EditDeliveryPlan.ApiInput = [
    {
      planId: parsedPlan.planId,
      name: parsedPlan.name,
      targetDriverId: parsedPlan.targetDriverId,
      orderedJobIds: parsedPlan.orderedJobIds,
      recurrence: parsedPlan.recurrence && {
        ...parsedPlan.recurrence,
        freq:
          parsedPlan.recurrence?.freq === FREQUENCY.BI_WEEKLY
            ? FREQUENCY.WEEKLY
            : parsedPlan.recurrence?.freq,
      },
      ...(parsedPlan.scheduledTime && {
        scheduledTime: moment(parsedPlan.scheduledTime).format(),
      }),
    },
  ]

  const editPlan = useEditPlanMutation()

  const getOrderStopIds = () => {
    const { currentStops, futureStops } = jobStopInfo

    const currentStopIdsUpdated = currentStops.map((stop) => stop.stopId)
    const orderedStopsPayload: Array<number> = [
      ...currentStopIdsUpdated.map(Number),
      ...futureStops.map((stop) => Number(stop.stopId)),
    ]
    return orderedStopsPayload
  }

  const handleEdit = (req: EditDeliveryPlan.ApiInput) => {
    const parsedReq = req.map((data) => {
      const recurrenceData = data.recurrence
      if (recurrenceData && !recurrenceData.until) {
        delete recurrenceData.until
      }
      if (recurrenceData && isEmpty(recurrenceData.daysOfWeek)) {
        delete recurrenceData.daysOfWeek
      }
      if (
        recurrenceData &&
        recurrenceData.freq &&
        recurrenceData.freq === FREQUENCY.BI_WEEKLY
      ) {
        recurrenceData.freq = FREQUENCY.WEEKLY
      }

      return { ...data, recurrence: recurrenceData }
    })

    //orderedJobIds need to be deleted because of Back End Validation
    //If orderedStopIds is present, no fields in orderedJobIds can be present
    delete parsedReq[0].orderedJobIds

    editPlan.mutate(parsedReq)
  }
  const handleOnChangeDate = (value: string | null) => {
    let getScheduledISOTime = null
    if (value && value !== '') {
      getScheduledISOTime = moment(value).format()
    }
    dataWithPlanId[0].scheduledTime = getScheduledISOTime
    handleEdit(dataWithPlanId)
  }

  const handleUpdateDateTime = () => {
    const newDate = scheduledTime ? moment(scheduledTime).toDate() : null
    const prevDate = parsedPlan?.scheduledTime
      ? moment(parsedPlan.scheduledTime).toDate()
      : null

    let scheduledDeliveryTs: string | null = null

    if (newDate === prevDate) {
      setIsDateTimeError('Cannot select the previous date/time')
      return
    }

    if (newDate) {
      if (moment(newDate).isSame(moment(prevDate))) {
        setIsDateTimeError('Cannot select the previous date/time')
        return
      }
      if (moment(newDate).isSameOrBefore(moment())) {
        setIsDateTimeError('Cannot select a passed date/time')
        return
      }
      scheduledDeliveryTs = moment(newDate).format(DELIVERY_DATE_TIME_PAYLOAD_FORMAT)
    }

    handleOnChangeDate(scheduledDeliveryTs)
    setIsDateTimeError(null)
  }

  const handleOnSelectDriver = (value: string | null) => {
    dataWithPlanId[0].targetDriverId = value
    handleEdit(dataWithPlanId)
  }
  const handleUpdateStopsOrderforPlan = () => {
    dataWithPlanId[0].orderedStopIds = getOrderStopIds()
    //orderedJobIds need to be deleted because of Back End Validation
    //If orderedStopIds is present, no fields in orderedJobIds can be present
    delete dataWithPlanId[0].orderedJobIds
    editPlan.mutate(dataWithPlanId, {
      onSettled: () => {
        dispatch(stoppedChangingJobStopsSequence())
        queryClient.invalidateQueries({
          queryKey: useDeliveryJobListByFilters.createKey({
            filters: { planId: focusedDeliveryPlanId || undefined },
          }),
        })
      },
    })
  }

  const handleImportButtonClick = () => {
    setShowModal(true)
    dispatch(selectedActiveImport(IMPORT_TYPE.PLAN))
  }

  useEffect(() => {
    setJobStopInfo(defaultStops)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    deliveryPlanStopInfo.parsedJobs,
    deliveryPlanStopInfo.currentStops,
    deliveryPlanStopInfo.futureStops,
    etaSimulateValue,
    defaultETAISOString,
  ])

  useEffect(() => {
    dispatch(unmountedDriverRightPanel())
  }, [dispatch])

  const moreOptionItems = useMemo(
    () => [
      {
        value: CONTEXTURE_MENU_VALUE.ASSIGN,
        label: ctIntl.formatMessage({
          id: 'delivery.rightPanel.planInfo.option.assignNow',
        }),
        onClick: handleAssignNow,
        disabled: !parsedPlan.targetDriverId || totalStops === 0,
      },
      {
        value: CONTEXTURE_MENU_VALUE.DUPLICATE,
        label: ctIntl.formatMessage({
          id: 'global.duplicate',
        }),
        onClick: () => {
          dispatch(clickedDuplicateDeliveryPlan(parsedPlan.planId))
        },
      },
      {
        value: CONTEXTURE_MENU_VALUE.DELETE,
        label: ctIntl.formatMessage({
          id: 'delivery.rightPanel.driverInfo.option.delete',
        }),
        onClick: handleOnDelete,
      },
    ],
    [parsedPlan, totalStops, handleAssignNow, handleOnDelete, dispatch],
  )

  const tabList = [
    {
      label: 'by Route',
      id: '1',
      hasBadge: true,
    },
    {
      label: 'by Job',
      id: '2',
      hasBadge: true,
    },
  ]

  const moveCard = useCallback<OnDragEndResponder>(
    ({ source, destination }) => {
      if (!destination) return
      const dragIndex = source.index
      const hoverIndex = destination.index
      const draggedItem = jobStopInfo.currentStops[dragIndex]
      setJobStopInfo((prevState) => {
        const newItems = prevState.currentStops.filter(
          (s) => s.stopId !== draggedItem.stopId,
        )
        newItems.splice(hoverIndex, 0, draggedItem)

        const updatedItem = newItems.map((item, index) => ({
          ...item,
          ordering: index + 1,
        }))

        //Set new dragging index after swap happened for ensuring an accurate swapBreakingPoint
        setDraggingIndex((_) => hoverIndex)

        dispatch(changedJobStopsSequence([...updatedItem]))
        return { ...prevState, currentStops: updatedItem }
      })
      setDraggingIndex(null)
    },
    [dispatch, jobStopInfo],
  )

  const { swapBreakingPoint, isDraggingPickupStop } = useMemo(() => {
    if (isNil(draggingIndex))
      return { swapBreakingPoint: null, isDraggingPickupStop: false }

    const draggedStop = jobStopInfo.currentStops[draggingIndex]
    const isDraggingPickupStop = draggedStop?.stopTypeId === JOB_STOP_TYPE_ID.PICKUP
    const correspondingStop = jobStopInfo.currentStops.find(
      (stop) =>
        stop.jobId === draggedStop.jobId && stop.ordering !== draggedStop.ordering,
    )

    return {
      swapBreakingPoint: correspondingStop
        ? correspondingStop.ordering - 1 // Ordering start from 1, so we need to minus the offset
        : null,
      isDraggingPickupStop,
    }
  }, [draggingIndex, jobStopInfo])

  useEffect(() => {
    setDraggingIndex(null)
    dispatch(stoppedChangingJobStopsSequence())
  }, [focusedDeliveryPlanId, dispatch])

  const deliveryPlanMetricCapacity = useDeliveryPlanCapacityData({
    planId: focusedDeliveryPlanId,
    maxWeight: Number(driver?.maxWeight) || 0,
    maxVolume: Number(driver?.maxVolume) || 0,
    scheduleShift: {
      start: driver?.shiftTimeStart || '',
      end: driver?.shiftTimeEnd || '',
    },
    deliveryCapabilities: driver?.deliveryCapabilities || [],
  })

  const statusDetailsData = useMemo(
    () =>
      formatMetricCardData({
        volume: deliveryPlanMetricCapacity?.volume,
        weight: deliveryPlanMetricCapacity?.weight,
        shiftSchedule: {
          start: driver?.shiftTimeStart || '',
          end: driver?.shiftTimeEnd || '',
        },
        driverCapabilities: deliveryPlanMetricCapacity?.driverCapabilities || [],
        isCapableToHandleLoad: deliveryPlanMetricCapacity?.isCapableToHandleLoad,
        isScheduleCapable: deliveryPlanMetricCapacity?.isScheduleCapable,
      }),

    [deliveryPlanMetricCapacity, driver?.shiftTimeStart, driver?.shiftTimeEnd],
  )

  const assignedJobsRef = useRef<HTMLDivElement>(null)
  const svgIconRef = useRef<HTMLDivElement>(null)

  useLayoutEffect(() => {
    const currRef = svgIconRef.current
    const currAssignedJobRef = assignedJobsRef.current
    if (currRef && currAssignedJobRef) {
      const rect = currRef.getBoundingClientRect()
      const rectAssignedJobRef = currAssignedJobRef.getBoundingClientRect()
      const centeredIconCalc =
        rect.top > window.innerHeight * 0.8
          ? rect.top
          : rect.top + (rect.top - rectAssignedJobRef.top)

      currRef.style.height = `calc(100vh - ${centeredIconCalc}px)`
    }
  }, [assignedJobsRef, jobStopInfo, isFoldedPlanDetails, currentTab])

  const totalJobs = useMemo(
    () =>
      (deliveryPlanStopInfo.deduplicatedJobs &&
        // eslint-disable-next-line
        // eslint-disable-next-line unicorn/explicit-length-check
        deliveryPlanStopInfo.deduplicatedJobs.length) ||
      0,
    [deliveryPlanStopInfo.deduplicatedJobs],
  )

  const isShowOptimizationWarning = useMemo(() => {
    if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.PICUP) {
      return totalJobs > Number.parseInt(deliverySettings.picupMaxJobs)
    } else if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.ROUTIFIC) {
      return totalJobs > Number.parseInt(deliverySettings.routificMaxJobs)
    }

    return false
  }, [
    totalJobs,
    deliverySettings.picupMaxJobs,
    deliverySettings.routificMaxJobs,
    deliverySettings.routingProviderApi,
  ])

  const optimizationMaxLimit = useMemo(() => {
    if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.PICUP) {
      return deliverySettings.picupMaxJobs
    } else if (deliverySettings.routingProviderApi === ROUTING_PROVIDER_API.ROUTIFIC) {
      return deliverySettings.routificMaxJobs
    }

    return 0
  }, [
    deliverySettings.picupMaxJobs,
    deliverySettings.routificMaxJobs,
    deliverySettings.routingProviderApi,
  ])

  const disableOptimizePlanStops = useMemo(() => {
    const undoneStatus = defaultStops.currentStops.filter(
      (stop) =>
        ![StopStatus.COMPLETED, StopStatus.REJECTED, StopStatus.ARRIVED].includes(
          stop.stopStatusId,
        ),
    )

    const stopsPD = undoneStatus.filter((stop) =>
      [JOB_STOP_TYPE_ID.PICKUP, JOB_STOP_TYPE_ID.DROPOFF].includes(stop.stopTypeId),
    )

    const stopsSingle = undoneStatus.filter((stop) =>
      [JOB_STOP_TYPE_ID.SINGLE].includes(stop.stopTypeId),
    )

    return (
      isEmpty(undoneStatus) ||
      (undoneStatus.length <= 2 && !isEmpty(stopsPD) && stopsPD.length <= 2) ||
      (undoneStatus.length === 1 && stopsSingle.length === 1)
    )
  }, [defaultStops.currentStops])

  const etaStatus = useMemo(() => {
    if (driver?.driverStatusId === DRIVER_STATUS_TO_ID.OFFLINE && !etaSimulateValue) {
      return ETA_SIMULATOR_STATUS_ID.DRIVER_UNLOCATED
    }
    return deliveryPlanStopInfo.driverETASimulationStatus
  }, [
    driver?.driverStatusId,
    deliveryPlanStopInfo.driverETASimulationStatus,
    etaSimulateValue,
  ])

  useEffectExceptOnMount(() => {
    if (isPlanNameToSave && planNameDebounce && planNameDebounce !== parsedPlan.name) {
      dataWithPlanId[0].name = planNameDebounce
      handleEdit(dataWithPlanId)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [planNameDebounce, isPlanNameToSave])

  const handleRecurrence = (value: boolean) => {
    if (value) {
      dataWithPlanId[0].recurrence = {
        freq: 'weekly',
        interval: 1,
      }
      setRecurrenceData(dataWithPlanId[0].recurrence)
    } else {
      dataWithPlanId[0].recurrence = null
      setRecurrenceData(null)
    }

    handleEdit(dataWithPlanId)
    setIsRecurrence(value)
    setExcludeDays([])
  }

  const toggleInputs = (toggleValue: boolean) => setToggleInput(!toggleValue)

  useEffect(() => {
    if (toggleInput) {
      inputRef?.current?.focus()
      inputRef?.current?.click()
    }
  }, [toggleInput])

  const frequencies = FREQUENCIES.map((frequency) => ({
    ...frequency,
    label: ctIntl.formatMessage({ id: frequency.label }),
  }))

  const { defaultRepeatBy, defaultExcludeDays } = useMemo(() => {
    const defaultRepeatBy =
      parsedPlan.recurrence &&
      parsedPlan.recurrence.freq &&
      parsedPlan.recurrence.interval === 2
        ? {
            value: FREQUENCY.BI_WEEKLY,
            label: ctIntl.formatMessage({ id: 'Bi-weekly' }),
          }
        : parsedPlan.recurrence &&
            parsedPlan.recurrence.freq &&
            parsedPlan.recurrence.interval === 1
          ? frequencies.find(
              (freq: Option) => freq.value === parsedPlan?.recurrence?.freq,
            ) || {
              value: FREQUENCY.WEEKLY,
              label: ctIntl.formatMessage({ id: 'Weekly' }),
            }
          : { value: FREQUENCY.WEEKLY, label: ctIntl.formatMessage({ id: 'Weekly' }) }
    const defaultExcludeDays =
      parsedPlan.recurrence && !isEmpty(parsedPlan.recurrence.daysOfWeek)
        ? DAYS.filter(
            (day) =>
              parsedPlan?.recurrence?.daysOfWeek &&
              !parsedPlan?.recurrence?.daysOfWeek.includes(day.value),
          )
        : []
    return {
      defaultRepeatBy,
      defaultExcludeDays,
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parsedPlan.recurrence])

  const [repeatBy, setRepeatBy] = useState<Option>(defaultRepeatBy)

  const [excludeDays, setExcludeDays] = useState(defaultExcludeDays)

  useEffect(() => {
    setPlanNameInputValue(parsedPlan.name)
    setRecurrenceData(parsedPlan.recurrence)
    setIsRecurrence(Boolean(parsedPlan.recurrence))
    setExcludeDays(defaultExcludeDays)
    setRepeatBy(defaultRepeatBy)
    setETASimulateValue(null)

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parsedPlan])

  useEffect(() => {
    if (parsedPlan.name !== dataWithPlanId[0].name) {
      setIsPlanNameToSave(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataWithPlanId[0].name, parsedPlan.name])

  const handleRepeatedDropdownUpdate = () => {
    dataWithPlanId[0].recurrence = recurrenceData
    handleEdit(dataWithPlanId)
  }

  const { routingMinVisitsPerVehicle, routingDynamicBalance } =
    useGetActiveRoutingSettings()

  return (
    <>
      {showModal && templates && (
        <DeliveryImport
          showModal={showModal}
          setShowModal={() => setShowModal(!showModal)}
          templates={templates}
        />
      )}
      <Box
        position="absolute"
        top="0px"
        width="100%"
        zIndex={`2`}
        background={themeType.light.styleRightPanelBackground}
        opacity="0.5"
        display={parsedPlan.assigning ? 'revert' : 'none'}
      />
      <Panel
        isDisabledScroll={true}
        isFoldable
        height="min-content"
        paddingBottom="0px"
        panelHeaderProps={{
          isStickyTop: true,
          title: parsedPlan.planId,
          moreActions: moreOptionItems,
        }}
        onFoldClick={() => setIsFoldedPlanDetails((prev) => !prev)}
        isFolded={isFoldedPlanDetails}
        children={null}
      />
      {!isFoldedPlanDetails && (
        <Box padding="5px 5px 20px 5px">
          <Box padding="5px 15px">
            <StyledInputField
              key={parsedPlan.planId}
              value={planNameInputValue}
              borderless
              onChange={(e) => {
                setPlanNameInputValue(e.target.value)
              }}
              onBlur={() => {
                if (planNameInputValue && planNameInputValue !== parsedPlan.name) {
                  dataWithPlanId[0].name = planNameInputValue
                  handleEdit(dataWithPlanId)
                } else {
                  setPlanNameInputValue(parsedPlan.name)
                }
                setIsPlanNameToSave(false)
              }}
            />
          </Box>
          <Box padding="5px 15px">
            <DateTimePicker
              value={scheduledTime}
              onChange={(value: any) => {
                setScheduledTime(value)
                if (!value) {
                  handleOnChangeDate(null)
                }
              }}
              onCalendarClose={handleUpdateDateTime}
              timeCaption={ctIntl.formatMessage({ id: 'Time' })}
              borderless
            />
            {isDateTimeError && (
              <HelperText>
                {ctIntl.formatMessage({
                  id: isDateTimeError,
                })}
              </HelperText>
            )}
          </Box>
          <Box padding="5px 15px">
            <DriverDropdown
              driverId={parsedPlan.targetDriverId}
              onChange={(option: any) => {
                if (option) {
                  handleOnSelectDriver(option.value || null)
                }
              }}
              borderless
              id={JSON.stringify(parsedPlan)}
            />
          </Box>
          {/* Recurrence checkbox */}
          <Box
            display="flex"
            alignItems="center"
          >
            <Box
              display="flex"
              padding="5px 15px"
            >
              <Tooltip
                align="right"
                tooltipContent={
                  <Box
                    color="white"
                    width="250px"
                  >
                    {ctIntl.formatMessage({
                      id: 'Repeat this plan based on the selected frequency. If selected, all jobs inside will be auto-duplicated in the same order after every plan was released to the target.',
                    })}
                  </Box>
                }
              >
                <Checkbox
                  label={ctIntl.formatMessage({
                    id: 'Recurring',
                  })}
                  cursor="default"
                  value={isRecurrence}
                  onChange={(value) => handleRecurrence(value)}
                />
              </Tooltip>
            </Box>

            {recurrenceData && (
              <>
                {!toggleInput && (
                  <Box paddingLeft="25px">
                    <Tooltip
                      align="right"
                      position="bottom"
                      tooltipContent={
                        <Box color="white">
                          <Box>
                            {ctIntl.formatMessage({ id: 'Repeat' })}:{' '}
                            {ctIntl.formatMessage({ id: repeatBy.label })}
                          </Box>
                          <Box>
                            {ctIntl.formatMessage({ id: 'Until' })}:{' '}
                            {ctIntl.formatMessage({
                              id: recurrenceData?.until || 'Never expires',
                            })}
                          </Box>
                          {!isEmpty(excludeDays) && (
                            <Box>
                              {ctIntl.formatMessage({ id: 'Exclude' })}:{' '}
                              {excludeDays.map(
                                (days, index) =>
                                  `${index ? ', ' : ''}${ctIntl.formatMessage({
                                    id: days?.label || '',
                                  })}`,
                              ) || []}
                            </Box>
                          )}
                        </Box>
                      }
                    >
                      <StyledInputField
                        onClick={() => {
                          toggleInputs(toggleInput)
                        }}
                        value={repeatBy.label}
                        iconBefore={RecurrenceIcon}
                        iconBeforeStyles={{
                          width: '25px',
                          height: '24px',
                          marginRight: '-25px',
                          zIndex: '1',
                        }}
                        inputStyle={{ paddingLeft: '22px' }}
                      />
                    </Tooltip>
                  </Box>
                )}

                {toggleInput && (
                  <Box paddingLeft="25px">
                    <RepeatedFieldDropdown
                      repeatBy={repeatBy}
                      repeatEndDate={recurrenceData?.until || ''}
                      excludeDays={excludeDays}
                      onCloseRepeatDropdown={() => {
                        toggleInputs(toggleInput)
                        handleRepeatedDropdownUpdate()
                      }}
                      repeatByOnChange={(value) => {
                        setRepeatBy(value)
                        setExcludeDays([])
                        recurrenceData.daysOfWeek = []
                        if (isRecurrence) {
                          if (value.value === FREQUENCY.BI_WEEKLY) {
                            recurrenceData.freq = FREQUENCY.WEEKLY
                            recurrenceData.interval = 2
                            delete recurrenceData.daysOfWeek
                          } else {
                            recurrenceData.freq = value.value
                            delete recurrenceData.interval
                          }
                        }
                      }}
                      untilDateOnChange={(value) => {
                        if (isRecurrence && value) {
                          recurrenceData.until = moment(value).format('YYYY-MM-DD')
                        } else {
                          recurrenceData.until = null
                        }
                      }}
                      excludeDaysOnChange={(value) => {
                        setExcludeDays(value)
                        if (isRecurrence) {
                          const extractSelectdaysOfWeek =
                            value &&
                            // eslint-disable-next-line
                            // eslint-disable-next-line unicorn/explicit-length-check
                            value.length &&
                            value.map((excludeDay: Option) => excludeDay.value)
                          recurrenceData.daysOfWeek =
                            extractSelectdaysOfWeek &&
                            extractSelectdaysOfWeek.length > 0
                              ? DAYS.filter(
                                  (day) => !extractSelectdaysOfWeek.includes(day.value),
                                ).map((days: Option) => days.value)
                              : []
                        }
                      }}
                    >
                      <InputField
                        ref={inputRef}
                        value={repeatBy.label}
                        iconBefore={RecurrenceIcon}
                        iconBeforeStyles={{
                          width: '25px',
                          height: '24px',
                          marginRight: '-25px',
                          zIndex: '1',
                        }}
                        inputStyle={{ paddingLeft: '22px' }}
                      />
                    </RepeatedFieldDropdown>
                  </Box>
                )}
              </>
            )}
          </Box>

          {/* Plan target driver metrics */}
          {parsedPlan.targetDriverId && (
            <Box padding="8px 15px">
              <MetricCheckCard
                data={statusDetailsData}
                title="Compatible Target"
                isOpened={isEmpty(parsedPlan.recurrence)}
              />
            </Box>
          )}
        </Box>
      )}
      <Panel
        isFlexibleHeight={false}
        isDisabledScroll={true}
        isFoldable
        panelHeaderProps={{
          isStickyTop: true,
          title: ctIntl.formatMessage(
            {
              id: 'ASSIGNED JOBS ({totalJobs})',
            },
            {
              values: {
                totalJobs:
                  (deliveryPlanStopInfo.deduplicatedJobs &&
                    // eslint-disable-next-line
                    // eslint-disable-next-line unicorn/explicit-length-check
                    deliveryPlanStopInfo.deduplicatedJobs.length) ||
                  0,
              },
            },
          ),
          customActions: (
            <>
              {parsedPlan.assigning ? (
                <LoadingIndicator
                  bubbleWidth={5}
                  bubbleHeight={5}
                  width="24px"
                  height="auto"
                  indicatorColor={`var(--styleActiveButtonsColour)`}
                />
              ) : (
                parsedPlan.targetDriverId &&
                totalStops > 0 && (
                  <HeaderCustomIconButton
                    padding="6px"
                    icon="check"
                    color={`var(--styleActiveButtonsColour)`}
                    tooltipMessage={ctIntl.formatMessage({
                      id: 'delivery.rightPanel.planInfo.option.assignNow',
                    })}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAssignNow()
                    }}
                  />
                )
              )}
              <HeaderIconButton
                icon="plus"
                padding="3px"
                tooltipMessage={ctIntl.formatMessage({
                  id: 'Add New Job',
                })}
                color={themeType['light'].styleDefaultIconColor}
                onClick={(e) => {
                  e.stopPropagation()
                  dispatch(resetjobStopsBeingCreated())
                  dispatch(clickedPlanPanelCreateJobDirectly())
                }}
              />
              <HeaderIconButton
                padding="6px"
                iconSvg={ImportSVG}
                iconSVGStyle={{ width: '21px', height: '21px' }}
                tooltipMessage={ctIntl.formatMessage({
                  id: 'Batch Import',
                })}
                color={themeType['light'].styleDefaultIconColor}
                onClick={(e) => {
                  e.stopPropagation()
                  handleImportButtonClick()
                }}
              />
            </>
          ),
        }}
        onFoldClick={() => setIsFoldedPlanJobStopDetails((prev) => !prev)}
        isFolded={isFoldedPlanJobStopDetails}
        nodeBetweenHeaderAndBody={
          currentTab === '1' &&
          isChangingJobStopsSequence && (
            <Box
              background={themeType['light'].styleRightPanelBackground}
              position="sticky"
              top="30px"
              zIndex="1"
              paddingLeft="20px"
              paddingRight="20px"
              width="100%"
              borderRadius="0 0 20px 20px"
            >
              <Box
                display="flex"
                gap="10px"
              >
                <Box padding="6px 0">
                  <Box
                    boxShadow="0 2px 5px 0 rgba(0,0,0,0.06);"
                    borderRadius="12px"
                  >
                    <Button
                      variant="danger"
                      isCapsule
                      onClick={() => {
                        setJobStopInfo(defaultStops)
                        dispatch(stoppedChangingJobStopsSequence())
                      }}
                    >
                      {ctIntl.formatMessage({
                        id: 'Discard',
                      })}
                    </Button>
                  </Box>
                </Box>
                <Box
                  padding="6px 0"
                  width="100%"
                >
                  <Box
                    boxShadow="0 2px 5px 0 rgb(0 0 0 / 18%);"
                    borderRadius="12px"
                  >
                    <Button
                      variant="primary"
                      isCapsule
                      width="100%"
                      onClick={() => {
                        handleUpdateStopsOrderforPlan()
                      }}
                      loading={{ isLoading: editPlan.isPending }}
                    >
                      {ctIntl.formatMessage({
                        id: 'Save Updates',
                      })}
                    </Button>
                  </Box>
                </Box>
              </Box>
            </Box>
          )
        }
      >
        <Box ref={assignedJobsRef}>
          {jobStopInfo.stops.length > 0 && (
            <Box
              paddingTop="18px"
              paddingLeft="20px"
              paddingRight="20px"
            >
              <Tabs
                activeTab={currentTab}
                tabs={tabList}
                onClick={(target: string) => setCurrentTab(target)}
                activeColor={`var(--styleActiveButtonsColour)`}
              />
            </Box>
          )}
          {jobStopInfo.currentStops.length > 0 && !isChangingJobStopsSequence && (
            <Box
              margin="9px 20px"
              background="white"
              borderRadius="5px"
              padding="3.5px 10px"
              boxShadow="0 2px 4px 0 rgba(0,0,0,0.05)"
              textAlign="left"
              opacity={disableOptimizePlanStops ? 0.5 : 1}
            >
              <RouteOptimizeButton
                onClick={() => {
                  const calcValidStopsToOptimize = jobStopInfo.currentStops.length
                  if (
                    [
                      ROUTING_PROVIDER_API.PICUP,
                      ROUTING_PROVIDER_API.ROUTIFIC,
                    ].includes(deliverySettings.routingProviderApi) &&
                    routingDynamicBalance === 'false' &&
                    !Number.isNaN(+routingMinVisitsPerVehicle) &&
                    +routingMinVisitsPerVehicle > calcValidStopsToOptimize
                  ) {
                    handleShowRoutingError(
                      calcValidStopsToOptimize,
                      +routingMinVisitsPerVehicle,
                    )
                  } else {
                    optimizePlanStops.mutate({
                      planId: Number(focusedDeliveryPlanId),
                    })
                  }
                }}
                disabled={optimizePlanStops.isPending || disableOptimizePlanStops}
                content={{
                  isLoading: optimizePlanStops.isPending,
                  text: optimizePlanStops.isPending ? (
                    ctIntl.formatMessage({
                      id: 'delivery.rightPanel.driver.optimizing',
                    })
                  ) : disableOptimizePlanStops ? (
                    <Box color={`var(--styleActiveButtonsColour)`}>
                      {ctIntl.formatMessage({
                        id: 'delivery.rightPanel.driver.optimize',
                      })}
                    </Box>
                  ) : (
                    ctIntl.formatMessage({
                      id: 'delivery.rightPanel.driver.optimize',
                    })
                  ),
                }}
              />
              {isShowOptimizationWarning && (
                <MuiTooltip
                  title={ctIntl.formatMessage(
                    {
                      id: 'There are {picupLimit} or more assigned jobs on the list. System may encounter difficulty with a large amount of jobs.',
                    },
                    {
                      values: {
                        picupLimit: optimizationMaxLimit,
                      },
                    },
                  )}
                >
                  <Box
                    margin="5px 0px"
                    padding="8px"
                    display="flex"
                    gap="10px"
                    alignItems="center"
                    fontSize="12px"
                    border="1px solid"
                    textAlign="left"
                    color={themeType.light.orange6}
                    borderColor={themeType.light.orange6}
                  >
                    <Icon icon="exclamation-triangle" />
                    <span>
                      {ctIntl.formatMessage(
                        {
                          id: 'Optimizing over {picupLimit} jobs may encounter difficulties',
                        },
                        {
                          values: {
                            picupLimit: optimizationMaxLimit,
                          },
                        },
                      )}
                    </span>
                  </Box>
                </MuiTooltip>
              )}
              {deliveryPlanStopInfo && deliveryPlanStopInfo.driverLocationETA && (
                <ETASimulator
                  etaStatus={etaStatus}
                  driverLocationETA={deliveryPlanStopInfo?.driverLocationETA}
                  refreshIconOnClick={() => {
                    setETASimulateValue(null)
                  }}
                  timepickerOnChange={(date) => {
                    setETASimulateValue(date)
                  }}
                  hasDriverLocation={false}
                />
              )}
            </Box>
          )}

          {currentTab === '1' ? (
            <>
              <DragDropContext
                onDragEnd={(result, provided) => {
                  setIsDragging(false)
                  const { destination } = result
                  if (!destination) return
                  if (
                    swapBreakingPoint === null ||
                    (isDraggingPickupStop
                      ? destination.index < swapBreakingPoint
                      : destination.index > swapBreakingPoint)
                  ) {
                    moveCard(result, provided)
                  }
                }}
                onDragStart={() => {
                  setIsDragging(true)
                }}
              >
                <Droppable droppableId="plan-current-stops">
                  {(provided) => (
                    <Box
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      paddingLeft="20px"
                      paddingRight="20px"
                    >
                      {jobStopInfo.currentStops.length > 0 && (
                        <Box paddingTop="16px">
                          {jobStopInfo.currentStops.map((item, index, stops) => {
                            const isCompleted =
                              item.stopStatusId === StopStatus.COMPLETED ||
                              item.stopStatusId === StopStatus.REJECTED ||
                              item.stopStatusId === StopStatus.STARTED ||
                              item.stopStatusId === StopStatus.ARRIVED

                            const phoneNumber = `${
                              item.contactCode && item.contactNumber
                                ? '+' + item.contactCode
                                : ''
                            }${item.contactNumber || ''}`

                            return (
                              <WithRightClickContextView
                                key={item.stopId}
                                stops={jobStopInfo.currentStops}
                                stop={item}
                                setJobStopInfo={setJobStopInfo}
                              >
                                <JobRouteCard
                                  key={item.stopId}
                                  index={index}
                                  data={{
                                    id: String(item.stopId),
                                    index,
                                    title: item.customerName,
                                    addressA: item.addressLine1,
                                    itemType: item.stopTypeId,
                                    mobileNumber: item.contactNumber,
                                    addressB: item.addressLine2,
                                    jobNumber: item.jobNumber,
                                  }}
                                  onChangingJobStopsSequence={
                                    startedChangingJobStopsSequenceHandler
                                  }
                                  isDraggable={!isCompleted}
                                  swapBreakingPoint={swapBreakingPoint}
                                  isDraggingPickupStop={isDraggingPickupStop}
                                  isDragging={isDragging}
                                >
                                  <JobStepItem
                                    title={
                                      item.customerName || item.customer.customerName
                                    }
                                    addressA={
                                      !item.lat || !item.lng
                                        ? ''
                                        : item.addressLine1 ||
                                          `GPS: ${item.lat}, ${item.lng}`
                                    }
                                    addressB={item.addressLine2}
                                    itemType={item.stopTypeId}
                                    mobileNumber={phoneNumber}
                                    jobNumber={item.jobNumber}
                                    statusColor={item.stopStatusId}
                                    isEndOfTimeLine={stops.length === index + 1}
                                    jobArrived={item.jobArrived || null}
                                    jobCompleted={item.jobCompleted || null}
                                    jobDuration={
                                      item.jobDuration
                                        ? `${item.jobDuration}${ctIntl.formatMessage({
                                            id: 'min',
                                          })}`
                                        : null
                                    }
                                    deliveryWindows={item.deliveryWindows}
                                    jobETA={item.legsETA}
                                    firstETA={
                                      jobStopInfo.currentStops.find(
                                        (stop) =>
                                          stop.legsETA && stop.legsETA !== 'unserved',
                                      )?.legsETA
                                    }
                                    onStopMouseEnter={() =>
                                      onDriverJobStopMouseOver(String(item.stopId))
                                    }
                                    onStopMouseLeave={() => onDriverJobStopMouseLeave()}
                                  />
                                </JobRouteCard>
                              </WithRightClickContextView>
                            )
                          })}
                        </Box>
                      )}

                      {!defaultStops.isPlanStopsLoading &&
                        jobStopInfo.stops.length === 0 && (
                          <RouteStatusWrapper ref={svgIconRef}>
                            <RouteDriverStatusSVG
                              {...makeSanitizedInnerHtmlProp({
                                dirtyHtml: DriverNoJobTodo,
                              })}
                            />
                            <RouterDriverText>
                              {ctIntl.formatMessage({
                                id: 'No job assigned',
                              })}
                            </RouterDriverText>
                          </RouteStatusWrapper>
                        )}
                      {defaultStops.isPlanStopsLoading && (
                        <RouteStatusWrapper ref={svgIconRef}>
                          <LoadingOverlay overlayColor="transparent" />
                        </RouteStatusWrapper>
                      )}
                      {provided.placeholder}
                    </Box>
                  )}
                </Droppable>
              </DragDropContext>
            </>
          ) : (
            <ThinScrollBarContainer
              paddingLeft="20px"
              paddingRight="20px"
            >
              {jobStopInfo.deduplicatedJobs &&
                jobStopInfo.deduplicatedJobs.map((item) => (
                  <JobItemContainer
                    key={item.jobId}
                    onClick={() => dispatch(clickedOverviewJobDetail([item.jobId]))}
                  >
                    <JobItem
                      job={{
                        jobData: {
                          ...item,
                          planName: parsedPlan.name,
                          planActive: parsedPlan.orderedJobIds?.length !== 0,
                        },
                        type: 'with-right-click-context',
                      }}
                      isActive={false}
                    />
                  </JobItemContainer>
                ))}
            </ThinScrollBarContainer>
          )}
        </Box>
      </Panel>
    </>
  )
}

const WithRightClickContextView = ({
  stops,
  stop,
  children,
  setJobStopInfo,
}: {
  stops: Array<JobStopReturn & { legsETA?: string | null }>
  stop: JobStopReturn & { legsETA?: string | null }
  children?: React.ReactNode
  setJobStopInfo: React.Dispatch<
    React.SetStateAction<{
      stops: Array<
        JobStopReturn & {
          legsETA?: string | null | undefined
        }
      >
      currentStops: Array<
        JobStopReturn & {
          legsETA?: string | null | undefined
        }
      >
      futureStops: Array<
        JobStopReturn & {
          legsETA?: string | null | undefined
        }
      >
      parsedJobs: FetchDeliveryJobListsByDriverId.Return['data']
      deduplicatedJobs: FetchDeliveryJobListsByDriverId.Return['data']
      isPlanStopsLoading: boolean
    }>
  >
}) => {
  const { contextItems, updatedStops } = usePlanStopsContextItems({ stops, stop })
  useEffect(
    () => setJobStopInfo((prevState) => ({ ...prevState, currentStops: updatedStops })),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [updatedStops],
  )
  return (
    <>
      <ContextMenu popoverItems={contextItems}>{children}</ContextMenu>
    </>
  )
}

const JobItemContainer = styled.div`
  background-color: white;
  margin-top: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.04);
  border-radius: 5px;
  padding: 5px 0px 3px;
  cursor: pointer;
  border: none;
  &:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  }
  &:active {
    box-shadow: 0 2px 5px 2px rgba(0, 0, 0, 0.2);
  }
  & .item-wrapper:hover {
    background-color: white;
  }
`

const StyledInputField = styled(InputField)`
  background: transparent;
  border: solid 1px transparent;
  &:hover {
    background: ${themeType.light.grey1};
    border: solid 1px transparent;
  }

  &:focus,
  &:active {
    background: white;
    border: solid 1px ${`var(--styleActiveButtonsColour)`};
  }
`
export default PlanViewPanel
