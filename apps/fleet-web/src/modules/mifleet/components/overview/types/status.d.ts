type MifleetGeneralId = string | number

type StatusDetail = {
  document_line_id: MifleetGeneralId
  fuel_validation_status_id: MifleetGeneralId
  plate: string
  manufacturer: string
  model: string
  document_concept_id: MifleetGeneralId
  document_concept: string
  document_concept_type_id: MifleetGeneralId
  document_concept_type: string
  expiration_date: string
  status: number
}

type Status = {
  vehicle_id: string
  plate: string
  manufacturer: string
  model: string
  supplier: string | null
  driver_name: string
  insurances_expiring: string
  fuel_validation_status_id: MifleetGeneralId | null
  document_id: number
  document_line_id: number
  insurances_expired: string
  fuelling_station?: string | number | null
  odometer: string | number | null
  fuelling_date_mileage: string | number | null
  fuelling_date: string
  validation_status: string
  vehicle_deleted: boolean
  validated: boolean | null
  fleet_check: boolean | null
  FEValidated: boolean | null
  litres: number
  permit_expiring: string
  permit_expired: string
  taxes_expiring: string
  taxes_expired: string
  services_expiring: string
  services_expired: string
  fines_expiring: string
  fines_expired: string
  merchant_flag_alert_level: null | string
  fuel_cards_expiring: string
  fuel_cards_expired: string
  total_value?: string | number | null
  consumption?: number | null
  document_status: DocumentStatus
  document_status_id: MifleetGeneralId
  source_id: string
  is_adblue: boolean
  additional_notes: string
  vehicle_group: string
  fuelling_order: string
  additional_notes: string
  description: string
  vehicle_tank_capacity: number | null
}

type GeoFuelStation = {
  address: string | null
  address2: string | null
  address3: string | null
  country: string | null
  fuel_station: string | null
  fuel_station_id: string | null
  has_car_wash: string | null
  has_restaurant: string | null
  has_toilets: string | null
  has_wifi: string | null
  is_24_hour: string | null
  is_automated_station: string | null
  is_deleted: string | null
  latitude: string | null
  longitude: string | null
  postal_code: string | null
  score: string | null
  supplier_fuel_station_code: string | null
  supplier_fuel_station_id: MifleetGeneralId | null
  supplier_name: string | null
}

type FuelStation = {
  distance: number
  fuel_station: string
  fuel_station_id: number
  latitude: number
  longitude: number
  score: number
  supplier_name: string | null
}

type TollStation = {
  distance: number
  is_deleted: string | null | boolean
  latitude: number
  longitude: number
  toll_station: string | null
  toll_station_id: number | null
}

type GeoFuelEntry = {
  check_date: string | null
  company_id: string
  driver_id: string | null
  fleet_fuel_litres: string | null
  fleet_is_tank_full: string | null
  fleet_latitude: string | null
  fleet_longitude: string | null
  fleet_odometer: number | null
  fuel_date: string
  fuel_litres: string | null
  fuel_station_address: string
  fuel_station_merchant: string
  fuel_station_validation_status_id: MifleetGeneralId | null
  fuel_validation_id: string
  fuel_validation_status_id: MifleetGeneralId | null
  is_deleted: string
  is_litres_checked: string | null
  is_odometer_checked: string | null
  is_tank_full: string | null
  odometer: string | null
  possible_fuel_stations: Array<FixMeAny> | null
  validated_fuel_station_id: MifleetGeneralId | null
  validation_date: string
  vehicle_coords: [{ fleet_latitude: string; fleet_longitude: string }] | [] | null
  vehicle_id: string
  vehicle_details: {
    plate: string
    manufacturer: string
    model: string
    is_deleted: string
  }
  driver_details?: {
    driver_name?: string
  }
}

type MiFleetResources = {
  drivers: [{ driver_id: string; short_name: string }] | []
  vehicles: [{ vehicle_id: string; plate: string }] | []
}

type MergedMarkers = {
  vehicle_latitude: number
  vehicle_longitude: number
  vehicle_position_description: string
  fuelling_station_latitude: number
  fuelling_station_longitude: number
  fuelling_station_position_description: string
  last_event_date: string
  last_event_odometer: number
  last_event_latitude: number
  last_event_longitude: number
  last_event_position_description: string
  next_event_date: string
  next_event_odometer: number
  next_event_latitude: number
  next_event_longitude: number
  next_event_position_description: string
  last_event_position_city: string
  next_event_position_city: string
  distance: number
}

type FraudDetail = {
  document_line_id: number
  validated: boolean | null
  validation_status: string
  validated_fuel_station_id: MifleetGeneralId | null
  validated_toll_station_id: MifleetGeneralId | null
  fuel_validation_status_id: MifleetGeneralId | null
  fleet_check_result_id: number | null
  toll_validation_status_id: MifleetGeneralId | null
  trip_id: string | null
  is_adblue: boolean
  vehicle_details: {
    vehicle_id: number
    plate: string
    manufacturer: string
    model: string
  }
  vehicle_coords: Array<{ fleet_latitude: number; fleet_longitude: number }>

  possible_fuel_stations: Array<FuelStation>
  possible_toll_stations: Array<TollStation>
  tank_capacity: {
    vehicle_tank_capacity: number
    document_line_liters: number
    diference: number
    diference_status: string
  }
  liters: {
    document_line_liters: number
    fleet_litres: number
    diference: number
    diference_status: string
  }
  distance: {
    distance: number
    distance_status: string
    vehicle_position: string
    fuel_station_position: string
  }
  merchantFlag: null | {
    merchant: string | number
    alertLevel: string
    stations: null | Array<{
      fuel_station: string
      latitude: number
      longitude: number
      distance: number
    }>
  }
  consumption: {
    consumption: number
    z: number
    median: number
    diference_status: string
    difference: number
  }
  show_tank_capacity_data: boolean
  show_liters_data: boolean
  show_distance_data: boolean
  show_liters_data: boolean
  sensor_malfunction: boolean
  vehicle_was_in_motion: boolean
  show_consumption: boolean
  other_transactions: {
    window: string
    count: number
    limit: number
    diference: number
    diference_status: string
    list: [
      {
        fuelling_id: number
        fuelling_date: string
        document_line_id: number
        quantity: number
        total_value: number
      },
    ]
  }
}
type FineFraudDetail = {
  document_line_id: number
  fine_validation_status_id: MifleetGeneralId | null
  fleet_check_result_id: number | null
  infringement_date: string
  infringement_location: string
  show_area_data: boolean
  validated: boolean | null
  validation_status: string
  vehicle_coords: Array<{ fleet_latitude: number; fleet_longitude: number }>
  show_driver_tag_events: boolean
  trip_id: string | null
  driver_tag_events: Array<{
    driver_name: string
    event_ts: string
    client_driver_tag_description: string
  }>
  vehicle_details: {
    vehicle_id: number
    plate: string
    manufacturer: string
    model: string
  }
  area: {
    coords: Array<{ lat: number; lng: number }>
    description: string
    area_status: 'red' | 'green'
  }
}
type ServiceReminder = {
  vehicle_id?: number | string
  service_id: string
  plate?: string
  service_type?: string
  service_date_interval?: string | null
  service_interval?: number | string | null
  odometer_warning?: number
  expiration_warning?: number
  odometer_diff?: number
  expiration_diff?: number
  status_level?: number | string
  vehicle_service_type_id?: string
  current_odometer?: number
  mileage_period?: number | null
  current_date?: string
  expiration_date?: string | null
  service_completed?: boolean
  succeeded_by_service_id?: null
  succeeded_mileage_pediod?: number
  succeeded_expiration_date?: date | null
  service_interval_unit?: string
}

type ServiceTask = {
  vehicle_service_type_id: string
  service_type: string
  company_id: string
  is_deleted: 'f' | 't'
  name: string
  value: string
}

type FuelFraudFuelStation = {
  document_line_id: number
  fuel_station: string
  supplier_name: string
  latitude: number
  longitude: number
  fuel_station_id?: number
  private?: boolean
}
