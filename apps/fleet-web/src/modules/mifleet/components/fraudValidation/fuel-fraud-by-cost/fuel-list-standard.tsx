import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import {
  Badge,
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  use<PERSON><PERSON>back<PERSON><PERSON>ed,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
  type GridRowParams,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import type { History } from 'history'
import { DateTime } from 'luxon'
import { useIntl } from 'react-intl'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'
import type { RouteComponentProps } from 'react-router-dom'

import type { MifleetReportReferredName } from 'api/types'
import { actions as reducerActions, selectors } from 'duxs/mifleet/overview/overview'
import { createComponentReducer } from 'duxs/utils'
import { fetchVehicleList, getVehicles } from 'duxs/vehicles'
import { useBindedActionsReducer, useModal } from 'src/hooks'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  useLengthInKmOrMilesCommonOptions,
  UserFormattedLengthInKmOrMiles,
} from 'src/modules/components/connected/UserFormattedLengthInKmOrMiles'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from 'src/modules/mifleet/api/costInput/shared/useCostsFilters'
import { useDeleteDocumentMutation } from 'src/modules/mifleet/lite/api/useMiFleetCost'
import {
  DocumentStatusOptions,
  useDefaultRangeFilterInNumber,
} from 'src/modules/mifleet/lite/helper'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from 'src/modules/mifleet/operational/shared/utils'
import DeleteSettingsDialog from 'src/modules/mifleet/shared/DeleteSettingsDialog'
import { CustomPagination } from 'src/modules/mifleet/shared/footer-dataGrid'
import type { AppState } from 'src/root-reducer'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { formatVolumeNumber } from 'src/util-components/formatted-volume'
import { getColumnWidth } from 'src/util-functions/table'

import { FormattedVolume, useMifleetFormattedNumber } from 'cartrack-ui-kit'
import AddNewCost from '../../../../mifleet/lite/import-data-new'
import { DOCUMENT_CONCEPT_FUELLING } from '../../../components/documents/concept-types'
import { FloatingPanelCostDetailDrawer } from '../../../operational/floating-panel-detail'
import { SourceFromSourceId, StatusValidation } from '../../../shared/utils'
import {
  FRAUD_STATUS_FILTER_OPTIONS,
  FRAUD_STATUS_PENDING_VALUE,
  getValidationStatusLabel,
} from '../shared/helpers'
import { FuelStationAlertCell } from '../shared/styled'

type DetailedVehicle = {
  plate: string
  manufacturer: string
  model: string
  vehicle_id?: number
}

type State = {
  detailsVehicle: DetailedVehicle
}

const initialState: State = {
  detailsVehicle: {
    plate: '',
    manufacturer: '',
    model: '',
    vehicle_id: undefined,
  },
}

const reducerObject = {
  setDetailsVehicle: (
    draft: State,
    {
      payload: detailsVehicle,
    }: {
      payload: DetailedVehicle
    },
  ) => {
    draft.detailsVehicle = detailsVehicle
  },
}

const reducer = createComponentReducer(reducerObject)

type Props = typeof actionCreators &
  ReturnType<typeof mapStateToProps> &
  RouteComponentProps & {
    history: History<FixMeAny>
    viewReportClick: (key: MifleetReportReferredName) => void
  }

function FuelStandardByCost(props: Props) {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsFuelFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <FuelFraudByCostContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

function FuelFraudByCostContent(
  props: Props & {
    costsFilters: MiFleetCostsFiltersWithDateInNumber | null
    setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
  },
) {
  const {
    fetchFuelList,
    fetchFuelDetails,
    fetchVehicleList,
    allVehicles,
    costsFilters,
    setCostsFilters,
    fuelList,
    updateFuelFraud,
  } = props
  const intl = useIntl()
  const columnHelper = useDataGridColumnHelper<Status>({ filterMode: 'client' })

  const [state, actions] = useBindedActionsReducer(reducer, reducerObject, initialState)

  const [detailsOpen, setDetailsOpen] = useState(false)
  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [itemToDelete, setItemToDelete] = useState<string | undefined>(undefined)

  const [isAddCostModalOpen, addCostModal] = useModal<any>(false)
  const [isEditCostModalOpen, editCostModal] = useModal<any>(false)
  const formatNumber = useMifleetFormattedNumber()
  const { generateLengthValue } = useLengthInKmOrMilesCommonOptions()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  const dateObject = useMemo(
    () =>
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
          }
        : null,
    [dateRangeFilter],
  )

  useEffect(() => {
    fetchFuelList(dateObject)
  }, [fetchFuelList, dateRangeFilter, dateObject])

  useEffect(() => {
    if (isEmpty(allVehicles)) {
      fetchVehicleList()
    }
  }, [allVehicles, fetchVehicleList])

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const FUEL_FRAUD_STATUS_FILTER_OPTIONS = useMemo(
    () =>
      FRAUD_STATUS_FILTER_OPTIONS.map((c) => ({
        value: c.value as typeof c.value,
        label: ctIntl.formatMessage({ id: c.label }),
      })).sort((a, b) => a.label.localeCompare(b.label)),
    [],
  )

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array: Array<Record<string, FixMeAny>> = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        fuelling_date,
        plate,
        document_status,
        source_id,
        supplier,
        fuelling_station,
        description,
        vehicle_group,
        additional_notes,
        fuelling_order,
        total_value,
        consumption,
        fuel_validation_status_id,
        litres: liters,
        merchant_flag_alert_level,
        odometer,
        vehicle_tank_capacity,
      } = d

      const fuelWarningLabel = () => {
        switch (merchant_flag_alert_level) {
          case 'green': {
            return ctIntl.formatMessage({ id: 'mifleet.fuel.fraud.risk.valid' })
          }

          case 'orange': {
            return ctIntl.formatMessage({
              id: 'mifleet.fuel.fraud.risk.moderate',
            })
          }

          case 'red': {
            return ctIntl.formatMessage({ id: 'mifleet.fuel.fraud.risk.high' })
          }

          default: {
            return ''
          }
        }
      }

      const consumptionKm = consumption && consumption / 100
      const consumptionL = consumption && 100 / consumption

      return {
        fuelling_date: fuelling_date
          ? DateTime.fromSQL(fuelling_date).toFormat('D t')
          : '',
        plate,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        supplier,
        fuelling_station,
        description,
        vehicle_group,
        additional_notes,
        fuelling_order,
        liters,
        total_value,
        consumption,
        consumptionKm,
        consumptionL,
        validation_status: ctIntl.formatMessage({
          id: getValidationStatusLabel(fuel_validation_status_id || '') || '',
        }),
        merchant_flag_alert_level: fuelWarningLabel(),
        odometer,
        vehicle_tank_capacity,
      }
    })

    const header = [
      'Date',
      'Vehicle',
      'Document Status',
      'Source',
      'Provider',
      'Station',
      'Description',
      'Vehicle Group',
      'Additional Notes',
      'Fuelling Order',
      'Liters',
      'Gross Total',
      'L/100km',
      'L/km',
      'Km/L',
      'mifleet.fuel.fraud.validation',
      'mifleet.fuel.fraud.fuel.station.warning',
      'odometer',
      'Tank Capacity',
    ]
    exportCostsToXLSX(
      header,
      data,
      getExportFileNameArray('Fuel', dateRangeFilter),
      'Fuel',
      getInitialExtraLineArray(dateRangeFilter),
    )
  }

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: itemToDelete },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }
  const getCostsList = useCallback(() => {
    fetchFuelList(dateObject)
  }, [dateObject, fetchFuelList])

  const columns = useMemo((): Array<GridColDef<Status>> => {
    const openDrawerDetailsForRow = (row: Status) => {
      actions.setDetailsVehicle({
        manufacturer: row.manufacturer,
        model: row.model,
        plate: row.plate,
        vehicle_id: row.vehicle_id,
        FEValidated: row.FEValidated,
        fleet_check: row.fleet_check,
        document_line_id: row.document_line_id,
      })
      fetchFuelDetails(row.document_line_id)
      setDetailsOpen(true)
      editCostModal.open()
    }

    return [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Date' }),
        field: 'fuelling_date',
        minWidth: 200,
        filterable: false,
        valueGetter: (_, row) => new Date(row.fuelling_date),
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        minWidth: 120,
        flex: 1,
        field: 'plate',
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id] || '',
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          minWidth: 100,
          flex: 1,
        },
      ),
      columnHelper.string((_, row) => row.supplier ?? '', {
        headerName: ctIntl.formatMessage({ id: 'Provider' }),
        field: 'supplier',
        flex: 1,
        minWidth: 120,
      }),
      columnHelper.string((_, row) => row.fuelling_station?.toString() ?? '', {
        headerName: ctIntl.formatMessage({ id: 'Station' }),
        field: 'fuelling_station',
        flex: 1,
        minWidth: 100,
      }),
      columnHelper.string((_, row) => row.description ?? '', {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        flex: 1,
        minWidth: 100,
      }),
      columnHelper.string((_, row) => row.vehicle_group ?? '', {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Group' }),
        field: 'vehicle_group',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string((_, row) => row.additional_notes ?? '', {
        headerName: ctIntl.formatMessage({ id: 'Additional Notes' }),
        field: 'additional_notes',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string((_, row) => row.fuelling_order ?? '', {
        headerName: ctIntl.formatMessage({ id: 'Fuelling Order' }),
        field: 'fuelling_order',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.number((_, row) => row.litres, {
        headerName: ctIntl.formatMessage({ id: 'Liters' }),
        field: 'litres',
        minWidth: 100,
        valueFormatter: (_, row) => row.litres.toFixed(2),
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        minWidth: 150,
        valueFormatter: (_, row) => formatNumber(row.total_value as number),
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.number(
        (_, row) =>
          row.consumption &&
          formatVolumeNumber({ intl, value: row.consumption, decimals: 2 }),
        {
          headerName: ctIntl.formatMessage({ id: 'L/100km' }),
          minWidth: 100,
          field: 'consumption',
          renderCell: ({ row }) =>
            row.consumption && (
              <FormattedVolume
                value={row.consumption}
                decimals={2}
              />
            ),
          align: 'right',
          headerAlign: 'left',
        },
      ),
      columnHelper.number(
        (_, row) =>
          row.consumption &&
          formatVolumeNumber({
            intl,
            value: Number(row.consumption) / 100,
            decimals: 2,
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'L/km' }),
          minWidth: 100,
          field: 'consumptionKm',
          renderCell: ({ row }) =>
            row.consumption && (
              <FormattedVolume
                value={Number(row.consumption) / 100}
                decimals={2}
              />
            ),
          align: 'right',
          headerAlign: 'left',
        },
      ),
      columnHelper.number(
        (_, row) =>
          row.consumption &&
          generateLengthValue(100 / Number(row.consumption), Math.round),
        {
          headerName: ctIntl.formatMessage({ id: 'km/L' }),
          minWidth: 100,
          field: 'consumptionL',
          renderCell: ({ row }) =>
            row.consumption && (
              <span>
                <UserFormattedLengthInKmOrMiles
                  valueInKm={100 / Number(row.consumption)}
                  transformValueBeforeFormatting={Math.round}
                />
              </span>
            ),
          align: 'right',
          headerAlign: 'left',
        },
      ),
      columnHelper.number((_, row) => (row.odometer ? Number(row.odometer) : null), {
        headerName: ctIntl.formatMessage({ id: 'odometer' }),
        minWidth: 100,
        field: 'odometer',
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.number(
        (_, row) =>
          row.vehicle_tank_capacity ? Number(row.vehicle_tank_capacity) : null,
        {
          headerName: ctIntl.formatMessage({ id: 'Tank Capacity' }),
          minWidth: 100,
          field: 'vehicle_tank_capacity',
          align: 'right',
          headerAlign: 'left',
        },
      ),
      columnHelper.singleSelect(
        (_, row) =>
          row.fuel_validation_status_id
            ? row.fuel_validation_status_id.toString()
            : FRAUD_STATUS_PENDING_VALUE,
        {
          headerName: ctIntl.formatMessage({ id: 'Fraud Status' }),
          width: getColumnWidth(
            fuelList,
            (row) => row.validation_status,
            ctIntl.formatMessage({ id: 'Fraud Status' }),
          ),
          field: 'fuel_validation_status_id',
          minWidth: 160,
          valueOptions: FUEL_FRAUD_STATUS_FILTER_OPTIONS,
          renderCell: ({ row }) => {
            const validationLabel = (
              <StatusValidation
                statusId={
                  row.fuel_validation_status_id
                    ? row.fuel_validation_status_id.toString()
                    : FRAUD_STATUS_PENDING_VALUE
                }
                source="operational"
              />
            )

            if (row.is_adblue) {
              return (
                <Badge
                  color="info"
                  badgeContent="AdBlue"
                  component="div"
                  sx={{ mt: 1 }}
                >
                  {validationLabel}
                </Badge>
              )
            }

            return validationLabel
          },
        },
      ),
      columnHelper.string((_, row) => row.merchant_flag_alert_level, {
        headerName: ctIntl.formatMessage({
          id: 'mifleet.fuel.fraud.fuel.station.warning',
        }),
        field: 'merchant_flag_alert_level',
        renderCell: ({ row }) => (
          <FuelStationAlertCell backgroundColor={row.merchant_flag_alert_level || ''} />
        ),
        minWidth: 160,
        flex: 1,
        align: 'center',
      }),
      {
        field: 'Actions',
        type: 'actions',
        filterable: false,
        sortable: false,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        valueGetter: (_, row) => row.document_id,
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            {Boolean(row.vehicle_id) && (
              <Tooltip
                title={`${ctIntl.formatMessage({
                  id: 'Validate Transaction',
                })}, ${ctIntl.formatMessage({ id: 'View Transaction' })}`}
                arrow
              >
                <IconButton
                  onClick={() => openDrawerDetailsForRow(row)}
                  color="secondary"
                  size="small"
                >
                  <RemoveRedEyeOutlinedIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => setItemToDelete(String(row.document_id))}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ]
  }, [
    columnHelper,
    fuelList,
    FUEL_FRAUD_STATUS_FILTER_OPTIONS,
    actions,
    fetchFuelDetails,
    editCostModal,
    formatNumber,
    intl,
    generateLengthValue,
  ])

  useEffect(() => {
    if (
      detailsOpen &&
      fuelList &&
      fuelList.length > 0 &&
      state?.detailsVehicle?.document_line_id
    ) {
      const currentActiveDetails = fuelList.filter(
        (f: FixMeAny) => f.document_line_id === state.detailsVehicle.document_line_id,
      )
      if (currentActiveDetails) {
        actions.setDetailsVehicle(...currentActiveDetails)
      }
    }
  }, [fuelList, detailsOpen, state.detailsVehicle, actions])

  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        dataGridId="mifleet-standard-fuel-fraud"
        sx={{
          '.MuiDataGrid-row .MuiDataGrid-cell': {
            span: {
              overflow: 'hidden !important',
              textOverflow: 'ellipsis !important',
            },
          },
        }}
        Component={DataGrid}
        rowSelectionModel={detailsOpen ? state.detailsVehicle.document_line_id : []}
        disableRowSelectionOnClick
        loading={props.loading}
        pagination
        rows={fuelList}
        getRowId={useCallbackBranded(
          (row: (typeof fuelList)[number]) => row.document_line_id,
          [],
        )}
        columns={columns}
        filterDebounceMs={50}
        onRowClick={({ row }: GridRowParams<Status>) => {
          actions.setDetailsVehicle({
            manufacturer: row.manufacturer,
            model: row.model,
            plate: row.plate,
            vehicle_id: row.vehicle_id,
            FEValidated: row.FEValidated,
            fleet_check: row.fleet_check,
            document_line_id: row.document_line_id,
          })
          fetchFuelDetails(row.document_line_id)
          setDetailsOpen(true)
          editCostModal.open()
        }}
        filterModel={filterModel}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        initialState={{
          columns: {
            columnVisibilityModel: {
              fuelling_order: false,
              additional_notes: false,
              consumptionKm: false,
              consumptionL: false,
              merchant_flag_alert_level: false,
              description: false,
              vehicle_group: false,
              odometer: false,
              vehicle_tank_capacity: false,
            },
          },
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  <Button
                    color="inherit"
                    variant="outlined"
                    size="small"
                    startIcon={<TrendingUpIcon />}
                    onClick={() =>
                      props.viewReportClick(
                        'REPORT_FUELLINGS' as MifleetReportReferredName,
                      )
                    }
                  >
                    {ctIntl.formatMessage({ id: 'Report' })}
                  </Button>
                  <Button
                    color="inherit"
                    variant="outlined"
                    size="small"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    size="small"
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Fuel Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Fuel',
          }}
        />
      )}
      {state.detailsVehicle && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setDetailsOpen(false)
            editCostModal.close()
          }}
          detailsCost={state.detailsVehicle}
          forceMenu={{
            name: 'Fuel',
            id: DOCUMENT_CONCEPT_FUELLING,
          }}
          detailedFraud={props.detailsFuelList}
          updateFraud={(validated: boolean) => {
            updateFuelFraud({
              document_line_id: state.detailsVehicle.document_line_id,
              fleet_check: state.detailsVehicle.fleet_check,
              validated,
              dateObject,
            })
          }}
          isSuccessUpdating={() => {
            setDetailsOpen(false)
            editCostModal.close()
            getCostsList()
          }}
          fraudIsLoading={props.detailsLoading}
          dateFilters={dateRangeFilter}
        />
      )}
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={[
            {
              name: 'Fuel',
              id: DOCUMENT_CONCEPT_FUELLING,
            },
          ]}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  fuelList: state.overview.fuelList,
  detailsFuelList: selectors.getDetailsFuelList(state),
  detailsLoading: selectors.isDetailsFuelLoading(state),
  loading: selectors.isFuelListLoading(state),
  allVehicles: getVehicles(state),
})

const actionCreators = {
  fetchVehicleList,
  fetchFuelList: reducerActions.fetchFuelList,
  fetchFuelDetails: reducerActions.fetchFuelDetails,
  updateFuelFraud: reducerActions.updateFuelFraud,
}

export default withRouter(connect(mapStateToProps, actionCreators)(FuelStandardByCost))

const DataGridHolder = styled(Box)({
  height: `100%`,
})
