import { useMemo, useRef, useState } from 'react'
import { Button, IconButton, keyframes, Stack, Tooltip } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined'
import MapOutlinedIcon from '@mui/icons-material/MapOutlined'
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'
import TableRowsOutlinedIcon from '@mui/icons-material/TableRowsOutlined'
import UploadOutlinedIcon from '@mui/icons-material/UploadOutlined'
import ViewTimelineIcon from '@mui/icons-material/ViewTimeline'
import { useDispatch } from 'react-redux'
import { useHistory, useLocation, useParams } from 'react-router'
import { match } from 'ts-pattern'

import { getDeliveryAppointmentsSetting } from 'duxs/user-sensitive-selectors'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import useDeliveryJobDetailsQuery from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import useActiveImport from 'src/modules/deliveryRevamp/hooks/useActiveImport'
import { useRefreshDeliveryData } from 'src/modules/deliveryRevamp/hooks/useRefresh'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import useImportJobTemplateList from '../../api/import-job-template/useImportJobTemplateList'
import { IMPORT_TYPE } from '../../constants'
import { useDeliveryMainPageContext } from '../../contexts/DeliveryMainPageContext'
import { DELIVERY_ONBOARDING_ELEMENT_IDS } from '../../contexts/DeliveryOnboardingContext'
import useClickOrSelectJobs from '../../hooks/useClickOrSelectJobs'
import useDeliveryPageSwitch from '../../hooks/useDeliveryPageSwitch'
import { clickedButtonToCreateANewJob } from '../../slice'
import { DELIVERY_PAGES } from '../../types'
import ButtonGroup from '../ButtonGroup'
import { getDeliverySettingsDialogMainPath } from '../SettingsDialog/utils'
import DateRangeSelector from './components/DateRangeSelector'
import DateSelector from './components/DateSelector'
import DriversPopper, { type DriversPopperHandles } from './components/DriversPopper'
import DeliveryImport from './components/ImportJobs'
import RecurringPopper, {
  type RecurringPopperHandles,
} from './components/RecurringPopper'
import ReportModal from './components/ReportModal'
import { HEADER_HEIGHT_IN_PX } from './constants'
import { jobIdToKeepFocused } from './utils'

const spin = keyframes`
0% {
  transform: rotate(0deg);
}
100% {
  transform: rotate(360deg);
}
`

export type PageOption = {
  name: string
  value: DELIVERY_PAGES
  icon: JSX.Element
}

export const PAGE_OPTIONS: Array<PageOption> = [
  {
    name: 'Map',
    value: DELIVERY_PAGES.MAP,
    icon: <MapOutlinedIcon sx={{ fontSize: 18 }} />,
  },
  {
    name: 'Table',
    value: DELIVERY_PAGES.TABLE,
    icon: <TableRowsOutlinedIcon sx={{ fontSize: 18 }} />,
  },
  {
    name: 'Timeline',
    value: DELIVERY_PAGES.TIMELINE,
    icon: <ViewTimelineIcon sx={{ fontSize: 18 }} />,
  },
]

const Header = () => {
  const history = useHistory()
  const location = useLocation()
  const dispatch = useDispatch()
  const { focusedJobId } = useClickOrSelectJobs()
  const mainPageContext = useDeliveryMainPageContext()
  const { data: templates } = useImportJobTemplateList()
  const deliveryPageSwitch = useDeliveryPageSwitch()

  const { page } = useParams<{ page: PageOption['value'] }>()

  const deliveryAppointmentsSetting = useTypedSelector(getDeliveryAppointmentsSetting)

  const filteredPageOptions = useMemo(
    () =>
      PAGE_OPTIONS.filter((option) => {
        const isTimelinePage = option.value === DELIVERY_PAGES.TIMELINE
        const shouldShowTimelinePage = deliveryAppointmentsSetting

        return !isTimelinePage || shouldShowTimelinePage
      }),
    [deliveryAppointmentsSetting],
  )

  const { data: jobDetails } = useDeliveryJobDetailsQuery(
    { jobIds: [focusedJobId as NonNullable<typeof focusedJobId>] },
    { enabled: focusedJobId !== null },
  )

  const { activeImport, handleSetActiveImport } = useActiveImport()
  const [showDownloadReport, setShowDownloadReport] = useState(false)

  const [headerEl, setHeaderEl] = useState<HTMLDivElement | null>(null)

  const { refresh, isLoading } = useRefreshDeliveryData({
    onSuccess() {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'All data has been refreshed successfully!',
        }),
        { variant: 'success' },
      )
    },
  })

  const recurringPopperRef = useRef<RecurringPopperHandles>(null)
  const driversPopperRef = useRef<DriversPopperHandles>(null)

  return (
    <Stack
      ref={setHeaderEl}
      direction="row"
      sx={(theme) => ({
        height: `${HEADER_HEIGHT_IN_PX}px`,
        width: '100%',
        borderBottom: '1px solid',
        borderColor: theme.palette.divider,
        backgroundColor: 'white',
        justifyContent: 'space-between',
        py: 2,
        px: 2.5,
      })}
    >
      {/* LEFT SIDE */}
      <Stack
        direction="row"
        gap={1}
        alignItems="center"
      >
        <Stack id={DELIVERY_ONBOARDING_ELEMENT_IDS.dateSelector}>
          {match(page)
            .with(DELIVERY_PAGES.MAP, DELIVERY_PAGES.TIMELINE, () => (
              <DateSelector
                selectedDate={mainPageContext.data.selectedDateRange?.start ?? null}
                onChange={(newDate) => {
                  mainPageContext.setData({
                    selectedDateRange:
                      newDate !== null ? { start: newDate, end: newDate } : null,
                  })
                }}
              />
            ))
            .with(DELIVERY_PAGES.TABLE, () => (
              <DateRangeSelector
                selectedDateRange={mainPageContext.data.selectedDateRange}
                onChange={(newDateRange) => {
                  mainPageContext.setData({
                    selectedDateRange: newDateRange,
                  })
                }}
              />
            ))
            .otherwise(() => null)}
        </Stack>

        <ButtonGroup
          value={page}
          options={filteredPageOptions}
          onChange={(_, newPage) => {
            const jobIdToRemainFocused =
              focusedJobId !== null && jobDetails
                ? (jobIdToKeepFocused(
                    focusedJobId,
                    jobDetails[focusedJobId].formData.scheduledDate,
                    mainPageContext.data.selectedDateRange?.start,
                  ) ?? null)
                : null

            deliveryPageSwitch(newPage, jobIdToRemainFocused)
          }}
          sx={{ height: '36px' }}
        />

        <Tooltip title={ctIntl.formatMessage({ id: 'Refresh' })}>
          <IconButton
            size="small"
            color="secondary"
            disabled={isLoading}
            onClick={refresh}
          >
            <RefreshOutlinedIcon
              sx={{
                fontSize: 20,
                ...(isLoading && {
                  animation: `${spin} 1s ease-in-out infinite`,
                }),
              }}
            />
          </IconButton>
        </Tooltip>

        {page === 'table' && (
          <Tooltip title={ctIntl.formatMessage({ id: 'Add New Job' })}>
            <IconButton
              size="small"
              color="secondary"
              disabled={isLoading}
              onClick={() => {
                dispatch(clickedButtonToCreateANewJob())
              }}
            >
              <AddIcon
                sx={{
                  fontSize: 20,
                }}
              />
            </IconButton>
          </Tooltip>
        )}
      </Stack>

      {/* RIGHT SIDE */}
      <Stack
        direction="row"
        gap={1}
        alignItems="center"
      >
        <Button
          id={DELIVERY_ONBOARDING_ELEMENT_IDS.headerImportButton}
          startIcon={<UploadOutlinedIcon sx={{ fontSize: 'inherit' }} />}
          color="secondary"
          variant="text"
          onClick={() => {
            handleSetActiveImport({ importType: IMPORT_TYPE.JOB })
          }}
        >
          {ctIntl.formatMessage({ id: 'Import' })}
        </Button>

        <Tooltip title={ctIntl.formatMessage({ id: 'Export' })}>
          <IconButton
            size="small"
            color="secondary"
            onClick={() => {
              setShowDownloadReport(true)
            }}
          >
            <DownloadOutlinedIcon sx={{ fontSize: 20 }} />
          </IconButton>
        </Tooltip>

        <RecurringPopper
          ref={recurringPopperRef}
          anchor={headerEl}
          onOpen={() => driversPopperRef.current?.close()}
        />

        <DriversPopper
          ref={driversPopperRef}
          anchor={headerEl}
          onOpen={() => recurringPopperRef.current?.close()}
        />

        <Tooltip title={ctIntl.formatMessage({ id: 'Settings' })}>
          <IconButton
            size="small"
            color="secondary"
            onClick={() => {
              history.push(getDeliverySettingsDialogMainPath(location))
            }}
          >
            <SettingsOutlinedIcon sx={{ fontSize: 20 }} />
          </IconButton>
        </Tooltip>
      </Stack>
      {showDownloadReport && (
        <ReportModal onClose={() => setShowDownloadReport(false)} />
      )}
      {!!activeImport && templates && (
        <DeliveryImport
          showModal={!!activeImport}
          setShowModal={() => handleSetActiveImport(null)}
          templates={templates}
        />
      )}
    </Stack>
  )
}

export default Header
